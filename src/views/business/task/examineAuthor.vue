<template>
  <div>
    <div id="star-layout-body-content">
      <div id="layout-content">
        <div id="publish-reconstruct">
          <div class="module-primary-box">
            <div class="publish-reconstruct module-primary-group" style="margin-bottom: 20px;">
              <div id="project" class="module-primary">
                <div class="module-secondary-group">
                  <!-- 基础信息骨架屏 -->
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="6" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 12" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的基础信息内容 -->
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">任务信息</div>
                      <div class="info-value">{{ taskDetail?.task?.task_name + '（ID:' + taskDetail?.task?.id + '）' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">项目信息</div>
                      <div class="info-value">{{ taskDetail?.project?.project_name + '（ID:' + taskDetail?.project?.id + '）' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">平台</div>
                      <div class="info-value">{{ platform[taskDetail?.task?.platform_type] }}</div>
                    </div>

                    <!-- 推广内容 - 仅在平台为抖音时展示 -->
                    <div class="info-item" v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2">
                      <div class="info-label">推广内容</div>
                      <div class="info-value">{{ taskDetail?.project?.promote_type == 1 ? '星立方' : '其他' }}</div>
                    </div>

                    <!-- 任务模式 - 仅在抖音和B站展示 -->
                    <div class="info-item" v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2 || taskDetail?.task?.platform_type === 5">
                      <div class="info-label">任务模式</div>
                      <div class="info-value">
                        <!-- 抖音平台显示指派/招募/推广 -->
                        <template v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2">
                          {{ taskModeDouyin[taskDetail?.task?.task_type] || '--' }}
                        </template>
                        <!-- B站显示京火/花火 -->
                        <template v-else-if="taskDetail?.task?.platform_type === 5">
                          {{ taskModeBilibili[taskDetail?.task?.task_type] || '--' }}
                        </template>
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">发起人</div>
                      <div class="info-value">{{ taskDetail?.task?.created_name }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">发起人部门</div>
                      <div class="info-value">{{ taskDetail?.task?.department_name }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">业绩归属人</div>
                      <div class="info-value">{{ taskDetail?.process_before?.operator }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">业绩归属人部门</div>
                      <div class="info-value">{{ taskDetail?.process_before?.department }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">客户名称</div>
                      <div class="info-value">{{ taskDetail?.project?.custom_company }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">任务创建时间</div>
                      <div class="info-value">{{ taskDetail?.task?.created_at }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">下单方式</div>
                      <div class="info-value">{{ taskDetail?.task?.order_method == 1 ? '星推下单' : '线下下单' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">订单类型</div>
                      <div class="info-value">{{ taskDetail?.task?.order_type == 1 ? '自运营' : taskDetail?.task?.order_type == 2 ? '走单（不含代理服务费）' : taskDetail?.task?.order_type == 3 ? '代下单（含代理服务费）' : taskDetail?.task?.order_type == 4 ? '资源包订单' : '水下订单' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">账户信息</div>
                      <div class="info-value">{{ taskDetail?.task?.account_name || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">合同信息</div>
                      <div class="info-value">{{ taskDetail?.process_before?.customer_contract}}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">充值合同信息</div>
                      <div class="info-value">{{ taskDetail?.task?.recharge_contract_id + '(¥' + taskDetail?.task?.oa_recharge_amount + ')'}}</div>
                    </div>
                  </div>

                </div>
              </div>
              
              <div id="financial-info" class="module-primary" style="margin-top: 20px;">
                <div class="module-secondary-group">
                  <!-- 财务信息骨架屏 -->
                  <div v-if="pageLoading" class="financial-info-skeleton">
                    <el-skeleton :rows="1" animated>
                      <template #template>
                        <div class="skeleton-financial-grid">
                          <div class="skeleton-financial-item" v-for="i in 3" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 120px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的财务信息内容 -->
                  <div v-else class="financial-info-grid">
                    <div class="info-item">
                      <div class="info-label">预估总金额</div>
                      <div class="info-value">¥{{ estimatedTotalAmount }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">总毛利</div>
                      <div class="info-value">¥{{ totalProfit }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">总毛利率</div>
                      <div class="info-value">{{ taskDetail?.task?.total_gross || '0.00' }}%</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 招募任务模式专用卡片 -->
              <!-- 总利润信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="profit-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  总利润信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="3" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 4" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">任务预估总预算</div>
                      <div class="info-value">¥{{ recruitmentTaskInfo?.assess_total_budget || 0 }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">期望合作达人数</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.expect_coop_num || 0 }}人</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">预估总毛利</div>
                      <div class="info-value">¥{{ recruitmentTaskInfo?.assess_total_gross || 0 }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">预估总毛利率</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.assess_total_gross_rate || 0 }}%</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 结算方式卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="settlement-method" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  结算方式
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 12" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">结算方式</div>
                      <div class="info-value">{{ getSettlementMethodText(recruitmentTaskInfo?.settlement_method) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">招募形式</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.recruit_type || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">选择达人团</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.selected_kol_type || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">保底模式</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.bottom_mode || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">考核指标</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.assess_indicator || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">达人保底价</div>
                      <div class="info-value">{{ formatPriceRange(recruitmentTaskInfo?.kol_bottom_min_price, recruitmentTaskInfo?.kol_bottom_max_price) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">达人一口价</div>
                      <div class="info-value">{{ formatPriceRange(recruitmentTaskInfo?.kol_min_price, recruitmentTaskInfo?.kol_max_price) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">CPM单价</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.cpm_price ? `¥${recruitmentTaskInfo.cpm_price}` : '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">DOU+流量是否计费</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.dou_is_cal_price || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">服务商选择方式</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.service_select_mode || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">服务商返点比例</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.service_rebate_rate ? `${recruitmentTaskInfo.service_rebate_rate}%` : '--' }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 利润信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="profit-calculation" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  利润信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="2" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 3" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">预估应收客户款</div>
                      <div class="info-value">¥{{ Number(recruitmentTaskInfo?.predict_receivable_customer_price || 0).toFixed(2) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">毛利</div>
                      <div class="info-value">¥{{ Number(recruitmentTaskInfo?.gross_profit || 0).toFixed(2) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">毛利率</div>
                      <div class="info-value">{{ Number(recruitmentTaskInfo?.gross_profit_margin || 0).toFixed(2) }}%</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 任务要求卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="task-requirements" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  任务要求
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="2" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 4" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item info-item-full">
                      <div class="info-label">镜头要求</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.lens_requirement || '--' }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">参考素材</div>
                      <div class="info-value">
                        <div v-if="referenceMaterialUrls.length > 0" class="reference-materials">
                          <img
                            v-for="(url, index) in referenceMaterialUrls"
                            :key="index"
                            :src="url"
                            class="reference-image"
                            @click="previewImage(url)"
                            alt="参考素材"
                          />
                        </div>
                        <span v-else>--</span>
                      </div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">报名日期</div>
                      <div class="info-value">{{ formatDateRange(recruitmentTaskInfo?.task_enroll_date) }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">期望保留时长</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.expect_save_time ? `${recruitmentTaskInfo.expect_save_time}天` : '--' }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 任务信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="task-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  任务信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 7" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">达人侧任务名称</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.kol_task_name || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">任务图标</div>
                      <div class="info-value">
                        <div v-if="taskIconUrls.length > 0" class="task-icons">
                          <img
                            v-for="(url, index) in taskIconUrls"
                            :key="index"
                            :src="url"
                            class="task-icon-image"
                            @click="previewImage(url)"
                            alt="任务图标"
                          />
                        </div>
                        <span v-else>--</span>
                      </div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">星图活动IP</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.xingtu_activity_ip || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">产品名称</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.product_name || '--' }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">产品介绍</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.product_desc || '--' }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">商品链接</div>
                      <div class="info-value">
                        <a v-if="recruitmentTaskInfo?.product_link" :href="recruitmentTaskInfo.product_link" target="_blank" class="product-link">
                          {{ recruitmentTaskInfo.product_link }}
                        </a>
                        <span v-else>--</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 组件信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="component-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  组件信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 8" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else class="basic-info-grid">
                    <div class="info-item info-item-full">
                      <div class="info-label">是否投放广告</div>
                      <div class="info-value">{{ formatAdvertisingTypes(recruitmentTaskInfo?.is_publish_adv) }}</div>
                    </div>
                    <div class="info-item" v-if="isAdvertisingTypeSelected('1')">
                      <div class="info-label">巨量广告主ID-效果广告</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.effect_adv_id || '--' }}</div>
                    </div>
                    <div class="info-item" v-if="isAdvertisingTypeSelected('2')">
                      <div class="info-label">巨量广告主ID-品牌广告</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.brand_adv_id || '--' }}</div>
                    </div>
                    <div class="info-item" v-if="isAdvertisingTypeSelected('3')">
                      <div class="info-label">巨量千川广告主ID</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.qianchuan_adv_id || '--' }}</div>
                    </div>
                    <div class="info-item" v-if="isAdvertisingTypeSelected('4')">
                      <div class="info-label">Dou+广告主ID</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.dou_adv_id || '--' }}</div>
                    </div>
                    <div class="info-item" v-if="isAdvertisingTypeSelected('4')">
                      <div class="info-label">Dou+广告主抖音UID</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.dou_adv_uid || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">组件名称</div>
                      <div class="info-value">{{ getComponentTypeName(recruitmentTaskInfo?.component_type) }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">组件文案</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.component_content || '--' }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">组件链接</div>
                      <div class="info-value">
                        <a v-if="recruitmentTaskInfo?.component_url" :href="recruitmentTaskInfo.component_url" target="_blank" class="component-link">
                          {{ recruitmentTaskInfo.component_url }}
                        </a>
                        <span v-else>--</span>
                      </div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">组件备注</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.component_remark || '--' }}</div>
                    </div>
                    <div class="info-item info-item-full">
                      <div class="info-label">其他特殊备注</div>
                      <div class="info-value">{{ recruitmentTaskInfo?.extra_remark || '--' }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div id="category" class="module-primary">
                <div class="module-primary-legend">
                  <div>达人信息</div>
                  <div>
                    <el-button @click="exportKolList"> 导出达人 </el-button>
                  </div>
                </div>
                <div class="module-secondary-group">
                  <!-- 达人信息骨架屏 -->
                  <div v-if="pageLoading" class="talent-info-skeleton">
                    <el-skeleton :rows="3" animated>
                      <template #template>
                        <div class="skeleton-talent-cards">
                          <div class="skeleton-talent-card" v-for="i in 3" :key="i">
                            <div class="skeleton-talent-header">
                              <div class="skeleton-talent-avatar">
                                <el-skeleton-item variant="circle" style="width: 60px; height: 60px;" />
                              </div>
                              <div class="skeleton-talent-info">
                                <el-skeleton-item variant="text" style="width: 120px; margin-bottom: 8px;" />
                                <el-skeleton-item variant="text" style="width: 200px; margin-bottom: 8px;" />
                                <el-skeleton-item variant="text" style="width: 180px;" />
                              </div>
                              <div class="skeleton-talent-metrics">
                                <div class="skeleton-metric" v-for="j in 3" :key="j">
                                  <el-skeleton-item variant="text" style="width: 60px; margin-bottom: 4px;" />
                                  <el-skeleton-item variant="text" style="width: 80px;" />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的达人信息内容 -->
                  <div v-else-if="selectionKolList?.length > 0" class="talent-card-list">
                    <el-card
                      v-for="(talent, index) in selectionKolList"
                      :key="index"
                      class="talent-card mb-4"
                      :class="{'warning-card': tableRowClassName({row: talent}) === 'warning-row'}"
                      @click="toggleCardExpand(talent, $event)"
                    >
                      <div class="talent-card-header">
                        <div class="talent-info">
                          <div class="avatar-container">
                            <img v-if="talent.kol_photo" :src="talent.kol_photo" class="talent-avatar" alt="" />
                            <img src="../../../assets/img/default-face.png" v-else class="talent-avatar" alt="" />
                          </div>
                          <div class="talent-basic-info">
                            <div class="talent-name">
                              {{ talent.kol_name }}
                              <el-tooltip
                                v-if="tableRowClassName({row: talent}) === 'warning-row'"
                                effect="light"
                                content="该达人毛利率低于15%或预估应收媒体返点比例（本次）低于达人历史最高返点比例，需要审核！"
                                placement="top"
                                :enterable="false"
                              >
                                <el-icon class="warning-icon"><Warning /></el-icon>
                              </el-tooltip>
                              <div class="talent-id">ID: {{ talent.platform_uid }}</div>
                            </div>
                            <div style="display: flex;">

                              <p class="talent-info-item" v-show="!isDouyinRecruitment(taskDetail?.task)">合作形式：<span>{{ placementListArr(talent) }}</span></p>
                              <p class="talent-info-item" v-show="isDouyinRecruitment(taskDetail?.task)">粉丝数量：<span>{{ taskDetail?.kols[index]?.ext?.kol_fans_num }}</span></p>
                              <p class="talent-info-item">任务名称：<span>{{ taskDetail?.kols[index]?.xingtu_task_name }}</span></p>
                              <p class="talent-info-item">MCN机构：<span>{{ talent?.mcn_short_name }}</span></p>
                            </div>
                            <!-- <div class="talent-type">{{ talent?.kol_attributes == 2 ? "机构达人" : "野生达人" }}</div> -->
                          </div>
                        </div>
                        <div class="talent-actions">
                          <div>
                            <p v-if="!isDouyinRecruitment(taskDetail?.task)">刊例价</p>
                            <p v-else>达人裸价(不含服务费)</p>
                            <p class="metric-value-p">¥{{ talent?.kol_price }}</p>
                          </div>
                          <div v-show="isDouyinRecruitment(taskDetail?.task)">
                            <p>是否为星晓计划达人</p>
                            <p class="metric-value-p" style="font-size: 14px !important;">{{ taskDetail?.kols[index]?.ext?.is_xingxiao_kol ? '是' : '否' }}</p>
                          </div>
                          <div v-show="!isDouyinRecruitment(taskDetail?.task)">
                            <p>预估应收媒体返点(本次)</p>
                            <p class="metric-value-p">{{talent?.process_info.kol_base_price ? Number(talent?.process_info.predict_receivable_medium_price / talent?.process_info.kol_base_price * 100 || 0).toFixed(2) : 0 }}%</p>
                          </div>
                          <div v-show="!isDouyinRecruitment(taskDetail?.task)">
                            <p>毛利率</p>
                            <p class="metric-value-p">{{ talent?.process_info?.gross_profit_margin }}%</p>
                          </div>
                          <el-button size="small" type="primary" plain @click.stop="talent.isExpanded = !talent.isExpanded">
                            {{ talent.isExpanded ? '收起' : '展开' }}
                          </el-button>
                          <el-button v-if="debug" size="small" type="warning" plain @click.stop="togglePlatformType(talent)">
                            切换平台 ({{ talent.platformType }})
                          </el-button>
                          <!-- <el-button size="small" @click="talent.isExpanded = !talent.isExpanded">查看下单信息</el-button> -->
                        </div>
                      </div>
                      
                      <!-- 展开后的详细信息 -->
                      <div v-if="talent.isExpanded" class="talent-details" @click.stop>
                        <el-divider />
                    
                        <connectInfo
                          :dialog-type="'info'"
                          :dialog-visible="true"
                          :process-info="prepareProcessInfo(talent)"
                          :orderType="1"
                          :rowItem="{
                            kol_price: talent.kol_price,
                            this_rebate_ratio: talent.this_rebate_ratio,
                            process_info: talent.process_info,
                            platform_key: talent.platform_key,
                            platform_id: talent.platform_id,
                            platform_type: talent.platform_type,
                            platform: talent.platform,
                            order_info: talent.order_info || {
                              ext: talent.ext || {}
                            }
                          }"
                          :orderMethod="2"
                          :embedded="true"
                          :platformType="Number(talent.platformType || 1)"
                          :taskType="taskDetail?.task?.task_type"
                        />
                      </div>
                    </el-card>
                  </div>
                  <div v-else-if="!pageLoading">
                    <el-empty description="暂无已选达人"> </el-empty>
                  </div>
                </div>
              </div>
              <!-- 新增审核记录板块 -->
              <div id="audit-records" class="module-primary">
                <div class="module-primary-legend">
                  <div>审核记录</div>
                </div>
                <div class="module-secondary-group">
                  <!-- 审核记录骨架屏 -->
                  <div v-if="pageLoading" class="audit-records-skeleton">
                    <el-skeleton :rows="2" animated>
                      <template #template>
                        <div class="skeleton-audit-timeline">
                          <div class="skeleton-audit-item" v-for="i in 2" :key="i">
                            <div class="skeleton-audit-marker">
                              <el-skeleton-item variant="circle" style="width: 24px; height: 24px;" />
                            </div>
                            <div class="skeleton-audit-content">
                              <div class="skeleton-audit-header">
                                <el-skeleton-item variant="text" style="width: 120px; margin-bottom: 8px;" />
                                <el-skeleton-item variant="text" style="width: 80px;" />
                              </div>
                              <el-skeleton-item variant="text" style="width: 200px; margin-top: 8px;" />
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的审核记录内容 -->
                  <div v-else-if="auditRecords.length > 0" class="audit-timeline">
                    <div class="audit-item" v-for="(review, index) in auditRecords" :key="index" :class="{'audit-item-success': review.reviewer_status === 1, 'audit-item-error': review.reviewer_status !== 1}">
                      <div class="audit-marker">
                        <div class="audit-icon">
                          <el-icon v-if="review.reviewer_status === 1"><Check /></el-icon>
                          <el-icon v-else><Close /></el-icon>
                        </div>
                        <div class="audit-line" v-if="index !== auditRecords.length - 1"></div>
                      </div>
                      <div class="audit-content">
                        <div class="audit-header">
                          <div class="audit-reviewer">
                            <span class="reviewer-name">{{ review.reviewer_personnel || '-' }}</span>
                            <el-tag size="small" :type="review.reviewer_status === 1 ? 'success' : 'danger'" class="status-tag">
                              {{ review.reviewer_status === 1 ? '审核通过' : '驳回' }}
                            </el-tag>
                          </div>
                          <div class="audit-time">{{ review.created_at || '-' }}</div>
                        </div>
                        <div class="audit-body">
                          <p class="audit-comment">{{ review.reviewer_comments || '无审核意见' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <el-empty v-else description="暂无审核记录"></el-empty>
                </div>
              </div>
            </div>
          </div>
          <div class="view-footer" style="z-index: 999" data-btm="c8297">
            <div class="footer-action">
              <el-button @click="cancelWriteOrder"> 取消 </el-button>
              <el-button type="danger" :disabled="!permission" @click="openRejectDialog">
                驳回
              </el-button>
              <el-button type="primary" :disabled="!permission" @click="openApproveDialog">
                审核通过
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogApproveVisible" title="审核通过" width="30%">
      <div>
        <span>审核备注<span style="color: #999;font-size: 12px;">（选填）</span></span>
        <el-input 
          type="textarea" 
          v-model="approveComments" 
          placeholder="可选填写审核通过的备注说明..." 
          class="mt-2"
          :disabled="approveLoading"
        ></el-input>
      </div>
      <template #footer>
        <div>
          <el-button @click="dialogApproveVisible = false" :disabled="approveLoading">取消</el-button>
          <el-button 
            @click="confirmApprove" 
            type="primary" 
            v-if="page_type != 'info'"
            :loading="approveLoading"
          >确认通过</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogRejectVisible" title="驳回任务" width="30%">
      <div>
        <span>驳回原因 <span style="color: #f56c6c;">*</span></span>
        <el-input 
          type="textarea" 
          v-model="rejectComments" 
          placeholder="请详细填写驳回原因，驳回时必须填写原因说明..." 
          class="mt-2"
          :disabled="rejectLoading"
        ></el-input>
      </div>
      <template #footer>
        <div>
          <el-button @click="dialogRejectVisible = false" :disabled="rejectLoading">取消</el-button>
          <el-button 
            @click="confirmReject" 
            type="danger" 
            v-if="page_type != 'info'"
            :loading="rejectLoading"
          >确认拒绝</el-button>
        </div>
      </template>
    </el-dialog>
    <connectInfo :dialog-type="'info'" :dialog-visible="dialogVisible" :process-info="processInfo" @cancel-write="cancelWrite">
    </connectInfo>
  </div>
</template>

<script setup>
import connectInfo from "../components/connectInfo.vue";
import { ref, onMounted, watch, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { taskProcessKolDetail, tasksApprove, exportKolListApi } from "@/api/modules/business";
import { useTabsStore } from "@/stores/modules/tabs";
import { Warning, Check, Close } from '@element-plus/icons-vue';
const tabStore = useTabsStore();
const route = useRoute();
const router = useRouter();
const submitButtonLoadding = ref(false);
const processInfo = ref({});
const approve = ref(true);
const comments = ref("");
const btnRoot = [];
const items = JSON.parse(localStorage.getItem("sidebar") || "[]");
items.map(item => {
  if (item.url.includes("/business/task")) {
    if (item.child) {
      item.child.map(i => {
        btnRoot.push(i.url);
      });
    }
  }
});
const formatNumber = (num) => {
  return num ? Number(num).toFixed(2) + "%" : "0%";
};
const kolList = ref(0);
const page_type = ref("");
const permission = ref();
const dialogVisible = ref(false);
const taskDetail = ref("");
const selectionKolList = ref([]);
const kolLastReviews = ref({});
const kolLastReviewerList = ref([]);
const task_status = ref(0);

// 页面加载状态
const pageLoading = ref(true);

// 招募任务信息
const recruitmentTaskInfo = ref({});

// 参考素材和任务图标URL数组
const referenceMaterialUrls = ref([]);
const taskIconUrls = ref([]);
const placementListArr = computed(() => {
  // Create different cooperation format lists based on platform type
  const platformMap = {
    // 小红书 (Xiaohongshu)
    3: {
      1: "图文笔记",
      2: "视频笔记",
      71: "视频笔记"
    },
    // 腾讯互选 (Tencent Mutual)
    6: {
      1: "1-60s",
      2: "60s以上",
      71: "60s以上"
    },
    // Default (星图/Douyin) - keep unchanged
    default: {
      1: "1-20S视频",
      2: "21-60S视频",
      71: "60S以上视频",
      100: "招募任务一口价"
    }
  };

  // Check if the current talent has a platformType
  return (talent) => {
    // Get the platform type from the talent, fallback to task platform type or default to 1 (Douyin)
    const platformType = talent?.platformType || taskDetail.value?.task?.platform_type || 1;
    
    // Return the appropriate format based on platform type
    return (platformMap[platformType] || platformMap.default)[talent.cooperation_type];
  };
});
const closeCurrentTab = () => {
  tabStore.removeTabs(route.fullPath);
};
const toggleCardExpand = (talent, event) => {
  // Don't toggle expansion if the click was on a button, interactive element, or inside the details section
  if (event.target.closest('.el-button') || 
      event.target.closest('.el-icon') || 
      event.target.closest('.talent-details')) {
    return;
  }
  talent.isExpanded = !talent.isExpanded;
};
const exportKolList = () => {
  exportKolListApi({
    task_id: taskDetail.value?.task?.id,
  }).then(res => {
    ElMessage.success('导出成功');
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '达人列表.xlsx';
    a.click();
    URL.revokeObjectURL(url);
  });
}
const cancelWrite = () => {
  dialogVisible.value = false;
};
const cancelWriteOrder = () => {
  closeCurrentTab();
  router.push({
    path: "/business/task"
  });
  closeCurrentTab();
};
const platform = {
  1: '抖音',
  2: '抖音星立方',
  3: '小红书',
  4: '快手',
  5: 'B站',
  6: '腾讯互选'
};


// 抖音任务模式映射
const taskModeDouyin = {
  1: '指派',
  2: '招募',
  3: '推广'
};

// B站任务模式映射
const taskModeBilibili = {
  1: '京火',
  2: '花火'
};

const debug = ref(false); // Debug flag to control debug output

// 招募任务相关辅助函数
const getSettlementMethodText = (method) => {
  const methodMap = {
    1: '按一口价结算',
    3: '按自然播放量结算'
  };
  return methodMap[method] || '--';
};

const formatPriceRange = (minPrice, maxPrice) => {
  if (!minPrice && !maxPrice) return '--';
  if (minPrice && maxPrice) return `¥${minPrice} - ¥${maxPrice}`;
  if (minPrice) return `¥${minPrice}`;
  if (maxPrice) return `¥${maxPrice}`;
  return '--';
};

const formatDateRange = (dateRange) => {
  if (!dateRange) return '--';
  try {
    const dates = typeof dateRange === 'string' ? JSON.parse(dateRange) : dateRange;
    if (Array.isArray(dates) && dates.length === 2) {
      return `${dates[0]} 至 ${dates[1]}`;
    }
  } catch (e) {
    console.error('解析日期范围失败:', e);
  }
  return '--';
};

const formatAdvertisingTypes = (types) => {
  if (!types) return '--';
  try {
    const typeArray = typeof types === 'string' ? JSON.parse(types) : types;
    if (!Array.isArray(typeArray) || typeArray.length === 0) return '--';

    const typeMap = {
      '1': '投放效果广告',
      '2': '投放品牌广告',
      '3': '投放巨量千川',
      '4': '投放至Dou+'
    };

    return typeArray.map(type => typeMap[type] || type).join('、');
  } catch (e) {
    console.error('解析广告类型失败:', e);
    return '--';
  }
};

const isAdvertisingTypeSelected = (type) => {
  if (!recruitmentTaskInfo.value?.is_publish_adv) return false;
  try {
    const typeArray = typeof recruitmentTaskInfo.value.is_publish_adv === 'string'
      ? JSON.parse(recruitmentTaskInfo.value.is_publish_adv)
      : recruitmentTaskInfo.value.is_publish_adv;
    return Array.isArray(typeArray) && typeArray.includes(type);
  } catch (e) {
    return false;
  }
};

const getComponentTypeName = (type) => {
  const typeMap = {
    1: '购物车',
    2: '落地页',
    3: '搜索组件',
    4: '其他'
  };
  return typeMap[type] || '--';
};

const previewImage = (url) => {
  // 简单的图片预览功能，可以后续扩展
  window.open(url, '_blank');
};

// 解析参考素材和任务图标URL
const parseFileUrls = (fileData) => {
  if (!fileData) return [];
  try {
    if (typeof fileData === 'string') {
      // 如果是JSON字符串，尝试解析
      const parsed = JSON.parse(fileData);
      if (Array.isArray(parsed)) {
        return parsed.map(item => typeof item === 'string' ? item : item.url).filter(Boolean);
      } else if (typeof parsed === 'string') {
        return [parsed];
      }
    } else if (Array.isArray(fileData)) {
      return fileData.map(item => typeof item === 'string' ? item : item.url).filter(Boolean);
    } else if (typeof fileData === 'string') {
      return [fileData];
    }
  } catch (e) {
    console.error('解析文件URL失败:', e);
  }
  return [];
};

const isDouyinRecruitment = (task) => {
  console.log(task);
  
  return (task.platform_type === 1 || task.platform_type === 2) && task.task_type === 2;
};

// 解析招募任务信息
const parseRecruitmentTaskInfo = (task) => {
  if (!task) return;

  // 检查是否为抖音平台的招募任务
  const isDouyinRecruitment = (task.platform_type === 1 || task.platform_type === 2) && task.task_type === 2;

  if (!isDouyinRecruitment) {
    recruitmentTaskInfo.value = {};
    referenceMaterialUrls.value = [];
    taskIconUrls.value = [];
    return;
  }

  try {
    // 解析task_info字段
    let taskInfo = {};
    if (task.task_info) {
      if (typeof task.task_info === 'string') {
        taskInfo = JSON.parse(task.task_info);
      } else {
        taskInfo = task.task_info;
      }
    }

    // 合并任务直接字段和task_info中的字段
    recruitmentTaskInfo.value = {
      // 总利润信息 - 优先使用task直接字段，兼容task_info
      assess_total_budget: task.assess_total_budget || taskInfo.assess_total_budget || 0,
      expect_coop_num: task.expect_coop_num || taskInfo.expect_coop_num || 0,
      assess_total_gross: task.assess_total_gross || taskInfo.assess_total_gross || 0,
      assess_total_gross_rate: task.assess_total_gross_rate || taskInfo.assess_total_gross_rate || 0,

      // 结算方式信息
      settlement_method: task.settlement_method || taskInfo.settlement_method || 0,
      recruit_type: task.recruit_type || taskInfo.recruit_type || '',
      selected_kol_type: task.selected_kol_type || taskInfo.selected_kol_type || '',
      bottom_mode: task.bottom_mode || taskInfo.bottom_mode || '',
      assess_indicator: task.assess_indicator || taskInfo.assess_indicator || '',
      kol_bottom_min_price: task.kol_bottom_min_price || taskInfo.kol_bottom_min_price || 0,
      kol_bottom_max_price: task.kol_bottom_max_price || taskInfo.kol_bottom_max_price || 0,
      kol_min_price: task.kol_min_price || taskInfo.kol_min_price || 0,
      kol_max_price: task.kol_max_price || taskInfo.kol_max_price || 0,
      cpm_price: task.cpm_price || taskInfo.cpm_price || 0,
      dou_is_cal_price: task.dou_is_cal_price || taskInfo.dou_is_cal_price || '',
      service_select_mode: task.service_select_mode || taskInfo.service_select_mode || '',
      service_rebate_rate: task.service_rebate_rate || taskInfo.service_rebate_rate || 0,
      enroll_date: task.enroll_date || taskInfo.enroll_date || '',

      // 任务要求信息
      lens_requirement: task.lens_requirement || taskInfo.lens_requirement || '',
      reference_material: task.reference_material || taskInfo.reference_material || '',
      task_enroll_date: task.task_enroll_date || taskInfo.task_enroll_date || '',
      expect_save_time: task.expect_save_time || taskInfo.expect_save_time || 0,

      // 任务信息
      kol_task_name: task.kol_task_name || taskInfo.kol_task_name || '',
      task_icon: task.task_icon || taskInfo.task_icon || '',
      xingtu_activity_ip: task.xingtu_activity_ip || taskInfo.xingtu_activity_ip || '',
      product_name: task.product_name || taskInfo.product_name || '',
      product_desc: task.product_desc || taskInfo.product_desc || '',
      product_link: task.product_link || taskInfo.product_link || '',

      // 组件信息
      is_publish_adv: task.is_publish_adv || taskInfo.is_publish_adv || '',
      effect_adv_id: task.effect_adv_id || taskInfo.effect_adv_id || '',
      brand_adv_id: task.brand_adv_id || taskInfo.brand_adv_id || '',
      qianchuan_adv_id: task.qianchuan_adv_id || taskInfo.qianchuan_adv_id || '',
      dou_adv_id: task.dou_adv_id || taskInfo.dou_adv_id || '',
      dou_adv_uid: task.dou_adv_uid || taskInfo.dou_adv_uid || '',
      component_type: task.component_type || taskInfo.component_type || 0,
      component_content: task.component_content || taskInfo.component_content || '',
      component_url: task.component_url || taskInfo.component_url || '',
      component_remark: task.component_remark || taskInfo.component_remark || '',
      extra_remark: task.extra_remark || taskInfo.extra_remark || '',

      // 利润计算相关字段
      kol_base_price: task.kol_base_price || taskInfo.kol_base_price || 0,
      provider_price: task.provider_price || taskInfo.provider_price || 0,
      predict_receivable_medium_ratio: task.predict_receivable_medium_ratio || taskInfo.predict_receivable_medium_ratio || 0,
      customer_rebate: task.customer_rebate || taskInfo.customer_rebate || 0,
      customer_service_price: task.customer_service_price || taskInfo.customer_service_price || 0,
      predict_receivable_customer_price: task.predict_receivable_customer_price || taskInfo.predict_receivable_customer_price || 0,
      gross_profit: task.gross_profit || taskInfo.gross_profit || 0,
      gross_profit_margin: task.gross_profit_margin || taskInfo.gross_profit_margin || 0
    };

    // 解析参考素材URL
    referenceMaterialUrls.value = parseFileUrls(recruitmentTaskInfo.value.reference_material);

    // 解析任务图标URL
    taskIconUrls.value = parseFileUrls(recruitmentTaskInfo.value.task_icon);

    console.log('解析招募任务信息完成:', recruitmentTaskInfo.value);
  } catch (error) {
    console.error('解析招募任务信息失败:', error);
    recruitmentTaskInfo.value = {};
    referenceMaterialUrls.value = [];
    taskIconUrls.value = [];
  }
};



const taskProcessKolDetailFun = id => {
  console.log("Debug mode:", debug.value);
  pageLoading.value = true; // 开始加载

  taskProcessKolDetail({ task_id: id }).then(res => {
    if (res.data == null) {
      ElMessage({
        message: res.msg,
        type: "error"
      });
      pageLoading.value = false; // 加载失败时关闭loading
      cancelWriteOrder();
      return;
    }

    // Extract platform information from API response if available
    let platformInfo = {
      project_platform: null,
      task_platform: null
    };
    
    // Safely extract platform info
    if (res.data && res.data.project) {
      platformInfo.project_platform = res.data.project.platform_id || res.data.project.platform_type;
    }
    
    if (res.data && res.data.task) {
      platformInfo.task_platform = res.data.task.platform_id || res.data.task.platform_type;
    }
    
    console.log("Platform information from API:", platformInfo);
    
    // Safely log the response data
    console.log("Complete API response structure:", 
      res.data ? Object.keys(res.data).join(', ') : 'No data',
      res.data && res.data.task ? 'Has task' : 'No task',
      res.data && res.data.project ? 'Has project' : 'No project'
    );
    
    // Try to find platform information in the response structure
    let globalPlatformType = null;
    
    // Check specific places for platform information - using a safer approach
    if (res.data && Object.prototype.hasOwnProperty.call(res.data, 'platform_id')) {
      globalPlatformType = res.data.platform_id;
    } else if (res.data && res.data.task && Object.prototype.hasOwnProperty.call(res.data.task, 'platform_id')) {
      globalPlatformType = res.data.task.platform_id;
    } else if (res.data && res.data.project && Object.prototype.hasOwnProperty.call(res.data.project, 'platform_id')) {
      globalPlatformType = res.data.project.platform_id;
    }
    
    console.log("Detected global platform type:", globalPlatformType);
    
    // Ensure each kol has platformType set correctly
    if (res.data && res.data.kols && Array.isArray(res.data.kols) && res.data.kols.length > 0) {
      res.data.kols = res.data.kols.map(kol => {
        if (!kol) {
          console.warn("Found null or undefined kol in data");
          return kol;
        }
        
        try {
          // Handle the ext data first - look in various places
          processExtData(kol);
          
          // Try to extract platform type from various possible sources
          let sources = {
            platform_key: kol.platform_key,
            platform_id: kol.platform_id,
            platform_type: kol.platform_type,
            platform: kol.platform
          };
          
          console.log(`Platform type sources for ${kol.kol_name || 'unnamed talent'}:`, sources);
          
          // Use the most specific platform info available, or default to 1 (Douyin)
          let rawPlatformType = kol.platform_key || kol.platform_id || kol.platform_type || kol.platform || 
                           globalPlatformType || platformInfo.project_platform || platformInfo.task_platform || 1;
          
          // Ensure platformType is a number
          kol.platformType = Number(rawPlatformType);
          
          // Handle any invalid conversions
          if (isNaN(kol.platformType)) {
            console.warn(`Invalid platform type for ${kol.kol_name || 'unnamed talent'}:`, rawPlatformType);
            kol.platformType = 1; // Default to Douyin
          }
          
          // Special case handling for specific platforms - for now just making sure values are reasonable
          if (kol.platformType <= 0 || kol.platformType > 10) {
            console.warn(`Unusual platform type value for ${kol.kol_name || 'unnamed talent'}:`, kol.platformType);
            kol.platformType = 1; // Default to Douyin if value is out of expected range
          }
                            
          console.log(`Setting platformType for ${kol.kol_name || 'unnamed talent'}: ${kol.platformType} (raw: ${rawPlatformType})`);
          
          // Use ext data to determine platform type if available
          if (kol.ext) {
            determineAndSetPlatformFromExt(kol);
          }
          
          // Allow URL parameter override for testing different platform types
          const urlParams = new URLSearchParams(window.location.search);
          const forcePlatform = urlParams.get('force_platform');
          if (forcePlatform) {
            console.log(`URL override: Forcing platform type to ${forcePlatform}`);
            kol.platformType = Number(forcePlatform);
          }
          
        } catch (error) {
          console.error("Error processing kol platform type:", error);
          kol.platformType = 1; // Default to Douyin on error
        }
        
        return kol;
      });
    } else {
      console.warn("No kols array found in response data or kols is empty");
    }

    selectionKolList.value = res.data.kols;
    console.log("API返回的达人数据:", {
      talents: res.data.kols.map(kol => ({
        name: kol.kol_name,
        hasProcessInfo: !!kol.process_info,
        processInfoKeys: kol.process_info ? Object.keys(kol.process_info) : [],
        platformKey: kol.platform_key,
        platformType: kol.platform_type,
        platform: kol.platform
      }))
    });
    
    // 初始化每个达人的展开状态
    selectionKolList.value.forEach(talent => {
      talent.isExpanded = false;
    });
    
    task_status.value = res.data.task.status;
    taskDetail.value = res.data;
    permission.value = res.permission;
    setDefaultSelection(res.data.kols);

    // 解析招募任务信息
    parseRecruitmentTaskInfo(res.data.task);

    // 数据加载完成，关闭骨架屏
    pageLoading.value = false;
  }).catch(error => {
    console.error('加载任务详情失败:', error);
    pageLoading.value = false; // 出错时也要关闭loading
  });
};
const approveLoading = ref(false);
const rejectLoading = ref(false);
const tasksApproveFun = (actionType) => {
  if (selectionKolList.value.some(item => item.approve?.toString() == undefined)) {
    ElMessage.error("存在未设置审核状态的达人");
    if (actionType === 'approve') {
      approveLoading.value = false;
    } else if (actionType === 'reject') {
      rejectLoading.value = false;
    }
    return;
  }
  
  let data = selectionKolList.value.map(item => {
    return {
      task_id: taskDetail.value.task.id,
      platform_uid: item.platform_uid,
      comments: item.comments || "",
      approve: item.approve == false ? false : true,
      reviewer_level: item.status,
      process_kol_id: item.process_kol_id
    };
  });
  
  console.log(data);
  submitButtonLoadding.value = true;
  tasksApprove(data).then(res => {
    // Reset loading states
    submitButtonLoadding.value = false;
    approveLoading.value = false;
    rejectLoading.value = false;
    
    // Close the appropriate dialog on success
    if (res.code == 990) {
      if (actionType === 'approve') {
        dialogApproveVisible.value = false;
      } else if (actionType === 'reject') {
        dialogRejectVisible.value = false;
      }
        cancelWriteOrder();
    }
  }).catch(error => {
    // Reset loading states on error
    console.error('API request failed:', error);
    submitButtonLoadding.value = false;
    approveLoading.value = false;
    rejectLoading.value = false;
    ElMessage.error("提交失败，请重试");
  });
};
const kolListRef = ref(null);
const setDefaultSelection = e => {
  nextTick(() => {
    if (kolListRef.value) {
      console.log(e, selectionKolList.value, "e");
      kolList.value = selectionKolList.value;
    }
  });
};

// 添加行样式类名函数，标识需要审核的达人
const tableRowClassName = (v) => {
  // 检查毛利率是否小于15%
  if (parseFloat(v?.row?.process_info?.gross_profit_margin ?? 0) < 15) {
    return "warning-row";
  }
  
  // 检查预估媒体返点比例是否低于历史最高返点
  if (v?.row?.this_rebate_ratio !== undefined && 
      v?.row?.rebate_ratio !== undefined && 
      parseFloat(v?.row?.this_rebate_ratio) < parseFloat(v?.row?.rebate_ratio)) {
    return "warning-row";
  }
  
  return "";
};

watch(
  () => selectionKolList.value,
  () => {
    kolList.value = selectionKolList.value;
  },
  { deep: true }
);

watch(
  () => approve.value,
  () => {
    comments.value = "";
  },
  { deep: true }
);

watch(
  () => permission.value,
  () => {
    if (task_status.value == 6) {
      ElMessage({
        message: "该任务已处理",
        type: "warning"
      });
      cancelWriteOrder();

    } else if (!permission.value) {
      ElMessage({
        message: "该任务已处理",
        type: "warning"
      });
      closeCurrentTab();
      router.push({
        path: "/business/task/informationAuthor",
        query: {
          page_type: "info",
          id: route.query.id
        }
      });
      closeCurrentTab();
    }
  },
  { deep: true }
);

const dialogApproveVisible = ref(false);
const dialogRejectVisible = ref(false);
const approveComments = ref("");
const rejectComments = ref("");

const openApproveDialog = () => {
  approveComments.value = "";
  dialogApproveVisible.value = true;
};

const openRejectDialog = () => {
  rejectComments.value = "";
  dialogRejectVisible.value = true;
};

const confirmApprove = () => {
  approveLoading.value = true;
  kolList.value = selectionKolList.value;
  kolLastReviewerList.value = kolList.value.map(v => {
    return {
      task_id: taskDetail.value.task.id,
      platform_uid: v.platform_uid,
      comments: approveComments.value,
      approve: true,
      reviewer_level: v.status,
      process_kol_id: v.process_kol_id,
      cooperation_type: v.cooperation_type
    };
  });
  
  selectionKolList.value = selectionKolList.value.map(item => {
    item.approve = true;
    item.comments = approveComments.value;
    return item;
  });
  
  tasksApproveFun('approve');
};

const confirmReject = () => {
  // 添加验证，如果拒绝原因为空则提示错误并返回
  if (!rejectComments.value || rejectComments.value.trim() === '') {
    ElMessage.error('请输入拒绝原因');
    return;
  }
  
  rejectLoading.value = true;
  kolList.value = selectionKolList.value;
  kolLastReviewerList.value = kolList.value.map(v => {
    return {
      task_id: taskDetail.value.task.id,
      platform_uid: v.platform_uid,
      comments: rejectComments.value,
      approve: false,
      reviewer_level: v.status,
      process_kol_id: v.process_kol_id,
      cooperation_type: v.cooperation_type
    };
  });
  
  selectionKolList.value = selectionKolList.value.map(item => {
    item.approve = false;
    item.comments = rejectComments.value;
    return item;
  });
  
  tasksApproveFun('reject');
};

const auditRecords = computed(() => {
  if (!selectionKolList.value.length) return [];
  
  const lastReviews = selectionKolList.value[0]?.log_reviews || {};
  const result = [];
  
  // 转换对象为数组并过滤掉空对象
  Object.keys(lastReviews).forEach(key => {
    if (Object.keys(lastReviews[key]).length > 0) {
      result.push(lastReviews[key]);
    }
  });
  
  // 不再进行前端排序，直接使用后端返回的顺序
  return result;
});

// 计算预估总金额
const estimatedTotalAmount = computed(() => {
  if (!taskDetail.value?.kols?.length) return '0.00';
  
  const totalAmount = taskDetail.value.kols.reduce((acc, curr) => {
    // 检查是否有必要的数据
    if (!curr) return acc;
    
    // 获取刊例价
    const kolBasePrice = Number(curr.kol_price || 0);
    // 获取达人授权费
    const kolLicensingFee = Number(curr.process_info?.kol_licensing_fee || 0);
    
    // 根据平台类型应用不同的计算公式
    let amount;
    if (curr.platformType === 3) { // 小红书平台
      amount = (kolBasePrice + kolLicensingFee) * 1.1;
    } else { // 其他平台
      amount = (kolBasePrice + kolLicensingFee) * 1.05;
    }
    
    return acc + amount;
  }, 0);
  
  return totalAmount.toFixed(2);
});

// 计算总毛利
const totalProfit = computed(() => {
  if (taskDetail.value?.kols?.length) {
    const totalGrossProfit = taskDetail.value.kols.reduce((sum, talent) => {
      if (!talent.process_info) return sum;
      
      const predictReceivableCustomerPrice = Number(talent.process_info.predict_receivable_customer_price || 0);
      const predictCost = Number(talent.process_info.predict_cost || 0);
      const grossProfit = predictReceivableCustomerPrice - predictCost;
      
      return sum + (grossProfit > 0 ? grossProfit : 0);
    }, 0);
    
    return totalGrossProfit.toFixed(2);
  }
  
  // 方法三：如果没有具体数据但有总金额和总毛利率，则通过公式计算：总毛利 = 总金额 × 总毛利率 ÷ 100%
  const totalAmount = parseFloat(estimatedTotalAmount.value);
  const grossRate = parseFloat(taskDetail.value?.task?.total_gross || 0);
  
  if (totalAmount && grossRate) {
    return (totalAmount * grossRate / 100).toFixed(2);
  }
  
  return '0.00';
});

// Add a new function to calculate gross profit for individual talents
const calculateGrossProfit = (talent) => {
  if (!talent || !talent.process_info) return '0.00';
  
  // Using the same formula as connectInfo.vue:
  // 毛利=预估应收客户款-预估成本
  const predictReceivableCustomerPrice = Number(talent.process_info.predict_receivable_customer_price || 0);
  const predictCost = Number(talent.process_info.predict_cost || 0);
  
  const grossProfit = predictReceivableCustomerPrice - predictCost;
  return grossProfit > 0 ? grossProfit.toFixed(2) : "0.00";
};

// Add this method to your script setup
const togglePlatformType = (talent) => {
  // Cycle through common platform types: 1 (Douyin), 3 (Xiaohongshu), 6 (Tencent Mutual)
  const platformTypes = [1, 3, 6];
  const currentIndex = platformTypes.indexOf(talent.platformType);
  const nextIndex = (currentIndex + 1) % platformTypes.length;
  talent.platformType = platformTypes[nextIndex];
  console.log(`Manually changed platformType for ${talent.kol_name} to ${talent.platformType}`);
};

const prepareProcessInfo = (talent) => {
  // Start with existing process_info or an empty object
  let processInfo = { ...(talent.process_info || {}) };
  
  // Ensure order_info exists
  if (!processInfo.order_info) {
    processInfo.order_info = {};
  }
  
  // IMPORTANT FIX: Include order_info data from the talent object if it exists
  if (talent.order_info) {
    processInfo.order_info = { ...processInfo.order_info, ...talent.order_info };
  }
  
  // Check if we have ext data either from talent directly or inside process_info
  if (talent.ext) {
    // If ext is directly on the talent, put it in the correct structure
    if (!processInfo.order_info.ext) {
      processInfo.order_info.ext = { ...talent.ext };
    } else {
      // Merge any existing ext with talent.ext
      processInfo.order_info.ext = { ...processInfo.order_info.ext, ...talent.ext };
    }
    
    if (debug.value) {
      console.log(`Added ext data from talent.ext to process_info for ${talent.kol_name}`);
    }
  }
  
  // Also check if there's platform-specific data in the talent object that needs to be mapped
  if (talent.platformType === 3) { // Xiaohongshu
    handleXiaohongshuData(talent, processInfo);
  } else if (talent.platformType === 6) { // Tencent Mutual
    handleTencentMutualData(talent, processInfo);
  }
  
  if (debug.value) {
    console.log(`Prepared processInfo for ${talent.kol_name}:`, 
      JSON.stringify({
        has_order_info: !!processInfo.order_info,
        has_ext: processInfo.order_info && !!processInfo.order_info.ext,
        ext_fields: processInfo.order_info && processInfo.order_info.ext ? Object.keys(processInfo.order_info.ext) : []
      }, null, 2)
    );
  }
  
  return processInfo;
};

// Helper function for Xiaohongshu-specific data
const handleXiaohongshuData = (talent, processInfo) => {
  if (!processInfo.order_info.ext) {
    processInfo.order_info.ext = {};
  }
  
  // Map Xiaohongshu-specific fields if they exist in the talent directly
  const xhsFields = [
    'xhs_type', 'xhs_brand_name', 'xhs_product_name', 
    'xhs_order_total_price', 'xhs_publish_date', 'xhs_ex_remark',
    'xhs_bind_spu', 'xhs_product_id'
  ];
  
  xhsFields.forEach(field => {
    if (talent[field]) {
      processInfo.order_info.ext[field] = talent[field];
    }
  });
};

// Helper function for Tencent Mutual-specific data
const handleTencentMutualData = (talent, processInfo) => {
  if (!processInfo.order_info.ext) {
    processInfo.order_info.ext = {};
  }
  
  // Map Tencent-specific fields if they exist in the talent directly
  const tencentFields = [
    'tencent_number', 'tencent_is_ry_talent', 'tencent_expect_pb_time',
    'tencent_order_total_price', 'tencent_product_name', 'tencent_product_desc',
    'tencent_video_rquirement', 'tencent_outside_video_desc', 'tencent_script_confirm',
    'tencent_promotion_scene', 'tencent_enable_adv_component', 'tencent_component_info',
    'tencent_enable_second_promotion'
  ];
  
  tencentFields.forEach(field => {
    if (talent[field]) {
      processInfo.order_info.ext[field] = talent[field];
    }
  });
};

// Helper function to process ext data from various places
const processExtData = (kol) => {
  // Check directly in top level ext
  if (kol.ext) {
    console.log(`Found ext at top level for ${kol.kol_name}`);
  }
  
  // Check in order_info
  if (kol.order_info && kol.order_info.ext) {
    console.log(`Found ext in order_info for ${kol.kol_name}`);
    if (!kol.ext) {
      kol.ext = { ...kol.order_info.ext };
    } else {
      kol.ext = { ...kol.ext, ...kol.order_info.ext };
    }
  }
  
  // Check in process_info
  if (kol.process_info) {
    // Check process_info.ext directly
    if (kol.process_info.ext) {
      console.log(`Found ext directly in process_info for ${kol.kol_name}`);
      if (!kol.ext) {
        kol.ext = { ...kol.process_info.ext };
      } else {
        kol.ext = { ...kol.ext, ...kol.process_info.ext };
      }
    }
    
    // Check in process_info.order_info
    if (kol.process_info.order_info && kol.process_info.order_info.ext) {
      console.log(`Found ext in process_info.order_info for ${kol.kol_name}`);
      if (!kol.ext) {
        kol.ext = { ...kol.process_info.order_info.ext };
      } else {
        kol.ext = { ...kol.ext, ...kol.process_info.order_info.ext };
      }
    }
  }
};

// Helper function to determine platform type from ext data
const determineAndSetPlatformFromExt = (kol) => {
  // Look for Xiaohongshu-specific fields
  if (kol.ext.xhs_type || kol.ext.xhs_brand_name || kol.ext.xhs_product_name) {
    console.log(`Detected Xiaohongshu platform from ext data for ${kol.kol_name}`);
    kol.platformType = 3;
    return true;
  }
  
  // Look for Tencent-specific fields
  if (kol.ext.tencent_number || kol.ext.tencent_product_name || kol.ext.tencent_is_ry_talent) {
    console.log(`Detected Tencent platform from ext data for ${kol.kol_name}`);
    kol.platformType = 6;
    return true;
  }
  
  return false;
};

onMounted(() => {
  if (route.query.id) {
    taskProcessKolDetailFun(route.query.id);
    nextTick(() => {
      kolList.value = selectionKolList.value;
    });
  } else {
    // 如果没有任务ID，直接关闭loading
    pageLoading.value = false;
  }
  if (route.query.page_type) {
    page_type.value = route.query.page_type;
  }
});
</script>

<style scoped>
@import "../index.scss";

.el-table th {
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: #fff !important;
}

/* 基础信息样式 */
.basic-info-grid, .financial-info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 12px;
  background-color: #fff;
}

.financial-info-grid {
  grid-template-columns: repeat(3, 1fr);
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.info-item-full {
  grid-column: 1 / -1;
}

.info-item-half {
  grid-column: span 2;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.info-value {
  font-size: 14px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 20px;
  line-height: 20px;
}

:deep(.el-step__title) {
  white-space: pre-line;
  font-size: 14px;
}

:deep(.el-step__title.is-finish) {
  color: black;
  font-size: 12px;
}

:deep(.el-step__description.is-finish) {
  white-space: pre-line;
  color: black;
  font-size: 12px;
  margin: 0 0 20px 20px;
}

:deep(.el-step__head.is-finish) {
  color: #000;
  border-color: #000;
  border-right: 1px solid rgba(0, 0, 0, 0);
  border-top: 1px solid rgba(0, 0, 0, 0);
  border-bottom: 1px solid rgba(0, 0, 0, 0);
}

/* 达人卡片列表样式 */
.talent-card-list {
  width: 100%;
}

.talent-card {
  margin-bottom: 16px;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.talent-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.warning-card {
  border-left: 4px solid #e6a23c;
  background-color: #fff9f0;
}

.talent-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.talent-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: 12px;
}

.talent-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #eee;
}

.talent-basic-info {
  display: flex;
  flex-direction: column;
}

.talent-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.talent-id {
  font-size: 12px;
  color: #909399;
}

.talent-type {
  font-size: 12px;
  color: #606266;
  background-color: #f0f2f5;
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
}

.talent-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  flex: 1;
  justify-content: center;
}

.metric-item {
  text-align: center;
  min-width: 80px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
}

.metric-value-p {
  font-size: 16px !important;
  color: var(--el-color-primary) !important;
  margin: 10px 0 0 0 !important;
}

.talent-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  p{
    font-size: 12px;
    margin: 0;
    color: #606266;
  }
}

.talent-details {
  margin-top: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 8px 0;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .talent-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .talent-info {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .talent-metrics {
    width: 100%;
    margin-bottom: 12px;
    justify-content: flex-start;
  }
  
  .talent-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 警告行的样式 */
:deep(.warning-row) {
  background-color: #fff9f0 !important;
  position: relative;
  cursor: help; /* 使用问号光标提示用户有信息 */
}

/* 当鼠标悬停时强化视觉效果 */
:deep(.warning-row:hover) {
  background-color: #ffefd1 !important;
}

/* 为了确保整行都有悬停效果 */
:deep(.warning-row > td) {
  position: relative;
}

/* 警告图标样式 */
.warning-icon {
  color: #e6a23c;
  font-size: 16px;
  cursor: help;
  margin-left: 4px;
}
/* 警告图标悬停效果 */
.warning-icon:hover {
  color: #d4921e;
}

/* 审核记录样式 */
.audit-description {
  padding: 10px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.talent-info-item{
  color: #606266;
  font-size: 14px;
  margin:10px 10px 0 0;
  span{
    color: #000;
  }
}

.audit-description p {
  margin: 5px 0;
}

:deep(.el-step) {
  margin-bottom: 10px;
}

:deep(.el-step__head.is-success) {
  color: #008b7d;
  border-color: #008b7d;
}

:deep(.el-step__head.is-error) {
  color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.el-step__title.is-success) {
  color: #008b7d;
}

:deep(.el-step__title.is-error) {
  color: #f56c6c;
}

#audit-records {
  margin-top: 20px;
}

/* 审核时间样式 */
.audit-time {
  color: #909399;
  font-size: 13px;
  margin-right: 20px;
}

/* 确保标题行能够容纳审核时间 */
:deep(.el-step__title) {
  width: 100%;
}

/* 新的审核记录时间线样式 */
.audit-timeline {
  position: relative;
  padding: 16px 0;
}

.audit-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.audit-item:last-child {
  margin-bottom: 0;
}

.audit-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  min-width: 24px;
}

.audit-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.audit-item-success .audit-icon {
  background-color: #e0f7f4;
  color: #008b7d;
}

.audit-item-error .audit-icon {
  background-color: #fef0f0;
  color: #f56c6c;
}

.audit-line {
  position: absolute;
  top: 24px;
  width: 2px;
  height: calc(100% + 24px);
  background-color: #ebeef5;
  z-index: 1;
}

.audit-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.audit-item-success .audit-content {
  border-left: 3px solid #008b7d;
}

.audit-item-error .audit-content {
  border-left: 3px solid #f56c6c;
}

.audit-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.audit-reviewer {
  display: flex;
  align-items: center;
}

.reviewer-name {
  font-weight: 500;
  margin-right: 8px;
  color: #303133;
}

.status-tag {
  font-size: 12px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

.audit-body {
  padding: 12px 16px;
}

.audit-comment {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-line;
}

/* 审核记录响应式样式 */
@media (max-width: 768px) {
  .audit-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .audit-time {
    margin-top: 8px;
    font-size: 12px;
  }
  
  .audit-reviewer {
    flex-wrap: wrap;
  }
  
  .reviewer-name {
    margin-bottom: 4px;
    width: 100%;
  }
  
  .audit-content {
    width: 100%;
  }
}

/* 骨架屏样式 */
.basic-info-skeleton {
  padding: 20px;
}

.skeleton-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.skeleton-info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.financial-info-skeleton {
  padding: 20px;
}

.skeleton-financial-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 16px;
}

.skeleton-financial-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.talent-info-skeleton {
  padding: 20px;
}

.skeleton-talent-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-talent-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background-color: #fff;
}

.skeleton-talent-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.skeleton-talent-avatar {
  flex-shrink: 0;
}

.skeleton-talent-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-talent-metrics {
  display: flex;
  gap: 24px;
  flex-shrink: 0;
}

.skeleton-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.audit-records-skeleton {
  padding: 20px;
}

.skeleton-audit-timeline {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.skeleton-audit-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.skeleton-audit-marker {
  flex-shrink: 0;
  margin-top: 4px;
}

.skeleton-audit-content {
  flex: 1;
}

.skeleton-audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 骨架屏动画优化 */
.el-skeleton__item {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: -100% 50%;
  }
}

/* 招募任务相关样式 */
.reference-materials {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.reference-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: transform 0.2s;
}

.reference-image:hover {
  transform: scale(1.1);
}

.task-icons {
  display: flex;
  gap: 8px;
}

.task-icon-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: transform 0.2s;
}

.task-icon-image:hover {
  transform: scale(1.1);
}

.product-link, .component-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.product-link:hover, .component-link:hover {
  text-decoration: underline;
}
</style>